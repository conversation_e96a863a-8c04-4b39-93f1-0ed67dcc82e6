# Payment Links is_promise Implementation Summary

## Overview
This implementation adds an `is_promise` column to the `payment_links` table to distinguish between:
- **Stripe invoices** (`is_promise = false`): Full amount invoices sent via outbound emails
- **Promise-to-pay links** (`is_promise = true`): Payment arrangements created by AI or user requests

## Changes Made

### 1. Database Schema Changes
- **File**: `bazzuka-dc-backend/supabase/migrations/add_is_promise_column.sql`
- Added `is_promise BOOLEAN DEFAULT false` column to `payment_links` table
- Updated existing records based on presence of `stripe_invoice_id`

### 2. Database Query Functions Updated
- **File**: `bazzuka-dc-backend/app/utils/supabase/queries.py`
- `insert_payment()`: Added `is_promise: bool = True` parameter
- `insert_payment_duplicate()`: Added `is_promise: bool = True` parameter  
- `get_payment_plans_by_issue_id()`: Added `promises_only: bool = False` parameter

### 3. Get Info Logic Updated
- **File**: `bazzuka-dc-backend/app/core/get_info.py`
- `sort_payment_plans_by_created_at()`: Added `promises_only=False` parameter
- Updated calls to only show promise-to-pay arrangements in get_info responses
- Modified `format_email_args_readable()` and `format_caller_inputs_readable()` to use `promises_only=True`

### 4. Stripe Invoice Creation
- **File**: `bazzuka-dc-backend/app/core/stripe.py`
- **New Function**: `create_stripe_invoice_for_full_amount(issue_id)`
  - Creates Stripe invoices for full outstanding amounts
  - Sets `is_promise=False` in database
  - Used for outbound emails
- Updated `create_payment_link()` to set `is_promise=True`
- Updated `create_invoice()` to set `is_promise=True`

### 5. Communication Manager Updated
- **File**: `bazzuka-dc-backend/app/core/comm_manager.py`
- `_invoke_outbound_email()`: Now creates Stripe invoices using `create_stripe_invoice_for_full_amount()`
- Replaces generic payment links with actual Stripe invoice URLs
- Returns error if Stripe invoice creation fails (no fallback to generic links)

### 6. Payment Tool Updates
- **File**: `bazzuka-dc-backend/app/core/payments.py`
- All `insert_payment_duplicate()` calls updated to include `is_promise=True`
- Maintains existing behavior for promise-to-pay arrangements

## Behavior Changes

### Outbound Emails (First Contact)
- **Before**: Used generic payment link template
- **After**: Creates Stripe invoice for full outstanding amount (`is_promise=false`)
- **Result**: Customers receive actual Stripe invoices via email

### AI/User Scheduled Payments
- **Before**: Created payment_links entries
- **After**: Creates payment_links entries with `is_promise=true`
- **Result**: These are marked as promise-to-pay arrangements

### Get Info Responses
- **Before**: Showed all payment_links for an issue
- **After**: Only shows payment_links where `is_promise=true`
- **Result**: AI only sees promise-to-pay arrangements, not Stripe invoices

## Migration Required

Before deploying, run the database migration:
```sql
-- Run the migration script
\i bazzuka-dc-backend/supabase/migrations/add_is_promise_column.sql
```

## Testing

Use the provided test script to verify implementation:
```bash
cd bazzuka-dc-backend
python test_is_promise_implementation.py
```

## Key Benefits

1. **Clear Separation**: Stripe invoices vs promise-to-pay links are now distinct
2. **Improved Email Flow**: Outbound emails send actual Stripe invoices
3. **Better AI Context**: AI only sees relevant promise-to-pay arrangements
4. **Backward Compatibility**: Existing functionality preserved
5. **Audit Trail**: Can track which payments are invoices vs promises

## Configuration Notes

- Default `is_promise=true` for new payment arrangements
- Default `is_promise=false` for Stripe invoices
- Existing records migrated based on `stripe_invoice_id` presence
- No changes required to frontend or API endpoints
