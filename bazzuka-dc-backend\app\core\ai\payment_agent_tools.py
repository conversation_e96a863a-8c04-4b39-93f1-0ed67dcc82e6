from app.ai.tools import Too<PERSON>, ToolEngine, ToolParam, ToolParams
from app.core.payments import PaymentsTool


schedule_payment_tool = Tool(
    "schedule_payment",
    "Schedule a payment for a particular issue. This should be called when the debtor explicitly agrees to make a payment (one-time or recurring). CRITICAL: Any payment amount higher than the outstanding balance will be REJECTED immediately. Only schedule payments for exact outstanding amounts or partial payments. For exact payments, the payment link is sent immediately. For partial payments and recurring payments, they require human approval first.",
    PaymentsTool.schedule_payment,
    ToolParams(
        [
            ToolParam(
                "issue_id",
                "number",
                "The issue_id of the case associated with the payment.",
                required=True,
            ),
            ToolParam(
                "amount",
                "number",
                "The amount of the payment in USD. For example, $300 would be 300. CRITICAL: This amount MUST NOT exceed the outstanding balance or the payment will be REJECTED. Only use amounts equal to or less than the outstanding balance.",
                required=True,
            ),
            ToolParam(
                "recurring",
                "boolean",
                "Whether the payment is recurring. If true, the payment will be scheduled to recur based on interval and interval_count. If false, the payment will be a one-time payment.",
                required=True,
            ),
            ToolParam(
                "settlement_discount",
                "boolean",
                "Whether the payment is a settlement discount. Use this when the debtor agrees to a reduced amount to settle the debt.",
                required=False,
            ),
            ToolParam(
                "start_date",
                "string",
                "Optional: For RECURRING payments, this is the first due date (when the first payment is due, format 'YYYY-MM-DD'). For ONE-TIME payments, this is when to send the payment link (use only if customer wants delayed sending). If not provided, defaults to today.",
                required=False,
            ),
            ToolParam(
                "due_date",
                "string",
                "For ONE-TIME payments only: the payment deadline in format 'YYYY-MM-DD'. For RECURRING payments, this is ignored. If not provided for one-time, defaults to 7 days from start_date or today.",
                required=False,
            ),
            ToolParam(
                "interval",
                "string",
                "The interval for recurring payments. Use 'week' for weekly/biweekly payments, 'month' for monthly payments. Required if recurring is true.",
                required=False,
            ),
            ToolParam(
                "interval_count",
                "number",
                "The number of intervals between payments. Use 1 for weekly/monthly, 2 for biweekly. Required if recurring is true.",
                required=False,
            ),
        ]
    ),
)

schedule_one_off_communication_tool = Tool(
    "schedule_one_off_communication",
    "Schedule a one-off communication with the defaulter. This should be used when the debtor requests to be contacted at a specific future time.",
    PaymentsTool.schedule_one_off_communication,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The defaulter's defaulter_id.",
                required=True,
            ),
            ToolParam(
                "channel",
                "string",
                "The communication channel. This can be 'email' or 'call'.",
                required=True,
            ),
            ToolParam(
                "date",
                "string",
                "The date of the communication in the format 'YYYY-MM-DD'.",
                required=True,
            ),
            ToolParam(
                "time",
                "string",
                "The time of the communication in the format 'HH:MM'.",
                required=True,
            ),
            ToolParam(
                "reason",
                "string",
                "The reason for the communication.",
                required=True,
            ),
            ToolParam(
                "is_human_followup",
                "boolean",
                "Whether the follow-up should be with a human (true) or AI (false). Defaults to false (AI).",
                required=False,
            ),
        ]
    ),
)

request_cancel_payment_arrangement_tool = Tool(
    "request_cancel_payment_arrangement",
    "Request cancellation of a payment arrangement. This will mark the arrangement as 'pending_cancellation' and require human approval before final cancellation. Use this if the debtor requests to cancel a scheduled or recurring payment plan. Optionally provide a reason for the cancellation.",
    PaymentsTool.request_cancel_payment_arrangement,
    ToolParams(
        [
            ToolParam(
                "payment_id",
                "string",
                "The ID of the payment arrangement to cancel.",
                required=True,
            ),
            ToolParam(
                "reason",
                "string",
                "Optional reason or comment for the cancellation request.",
                required=False,
            ),
        ]
    ),
)

resend_payment_links_tool = Tool(
    "resend_payment_links",
    "Resend all approved and unpaid payment links for an issue to the defaulter's email. Use this when the debtor requests to have their payment links resent or if they mention they didn't receive the payment links.",
    PaymentsTool.resend_payment_links,
    ToolParams(
        [
            ToolParam(
                "issue_id",
                "string",
                "The issue_id to resend the associated payment link(s) for.",
                required=True,
            ),
        ]
    ),
)


def make_payment_agent_tool_engine():
    """Create a tool engine specifically for the payment agent with focused payment and communication tools."""
    engine = ToolEngine(PaymentsTool)
    engine.register(schedule_payment_tool)
    engine.register(schedule_one_off_communication_tool)
    engine.register(request_cancel_payment_arrangement_tool)
    engine.register(resend_payment_links_tool)
    return engine


payment_agent_tool_engine = make_payment_agent_tool_engine() 
